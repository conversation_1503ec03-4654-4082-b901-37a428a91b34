// Improved Common utilities for product data extraction
// Fixed injection and timing issues

(function() {
  'use strict';
  
  // Ensure we don't overwrite existing CommonExtractor
  if (window.CommonExtractor) {
    console.log('CommonExtractor already exists, skipping initialization');
    return;
  }

  console.log('Initializing CommonExtractor...');

  const CommonExtractor = {
    // Debug flag
    debug: true,
    
    // Log debug messages
    log(...args) {
      if (this.debug) {
        console.log('[CommonExtractor]', ...args);
      }
    },

    // Wait for element to appear in DOM with better error handling
    waitForElement(selector, timeout = 5000) {
      this.log(`Waiting for element: ${selector}`);
      
      return new Promise((resolve, reject) => {
        // Check if element already exists
        const element = document.querySelector(selector);
        if (element) {
          this.log(`Element found immediately: ${selector}`);
          resolve(element);
          return;
        }

        let attempts = 0;
        const maxAttempts = Math.floor(timeout / 100);

        // Use interval instead of MutationObserver for more reliability
        const checkInterval = setInterval(() => {
          attempts++;
          const element = document.querySelector(selector);
          
          if (element) {
            this.log(`Element found after ${attempts} attempts: ${selector}`);
            clearInterval(checkInterval);
            resolve(element);
            return;
          }

          if (attempts >= maxAttempts) {
            clearInterval(checkInterval);
            this.log(`Element not found after ${timeout}ms: ${selector}`);
            reject(new Error(`Element ${selector} not found within ${timeout}ms`));
          }
        }, 100);
      });
    },

    // Extract text content safely with multiple fallback strategies
    extractText(selector, defaultValue = '') {
      this.log(`Extracting text from: ${selector}`);
      
      try {
        // Try multiple selection strategies
        let element = document.querySelector(selector);
        
        // If not found, try without data attributes
        if (!element && selector.includes('[data-')) {
          const fallbackSelector = selector.replace(/\[data-[^\]]+\]/g, '').trim();
          if (fallbackSelector) {
            element = document.querySelector(fallbackSelector);
            this.log(`Fallback selector used: ${fallbackSelector}`);
          }
        }
        
        if (element) {
          const text = element.textContent || element.innerText || '';
          const cleanedText = this.cleanText(text);
          this.log(`Extracted text: "${cleanedText}"`);
          return cleanedText || defaultValue;
        } else {
          this.log(`Element not found: ${selector}`);
          return defaultValue;
        }
      } catch (error) {
        console.warn(`Failed to extract text from ${selector}:`, error);
        return defaultValue;
      }
    },

    // Extract attribute value safely
    extractAttribute(selector, attribute, defaultValue = '') {
      this.log(`Extracting ${attribute} from: ${selector}`);
      
      try {
        const element = document.querySelector(selector);
        if (element) {
          const value = element.getAttribute(attribute) || defaultValue;
          this.log(`Extracted ${attribute}: "${value}"`);
          return value;
        } else {
          this.log(`Element not found: ${selector}`);
          return defaultValue;
        }
      } catch (error) {
        console.warn(`Failed to extract ${attribute} from ${selector}:`, error);
        return defaultValue;
      }
    },

    // Extract multiple elements with better error handling
    extractMultiple(selector, extractor) {
      this.log(`Extracting multiple elements: ${selector}`);
      
      try {
        const elements = document.querySelectorAll(selector);
        this.log(`Found ${elements.length} elements`);
        
        const results = Array.from(elements).map((element, index) => {
          try {
            return extractor(element, index);
          } catch (error) {
            console.warn(`Error processing element ${index}:`, error);
            return null;
          }
        }).filter(Boolean);
        
        this.log(`Successfully processed ${results.length} elements`);
        return results;
      } catch (error) {
        console.warn(`Failed to extract multiple elements from ${selector}:`, error);
        return [];
      }
    },

    // Clean and normalize text with better handling
    cleanText(text) {
      if (!text) return '';
      
      return text
        .replace(/\s+/g, ' ')        // Multiple spaces to single space
        .replace(/\n+/g, ' ')        // Newlines to space  
        .replace(/\t+/g, ' ')        // Tabs to space
        .replace(/\u00A0/g, ' ')     // Non-breaking spaces
        .trim();
    },

    // Extract images with better URL handling
    extractImages(selectors) {
      this.log('Extracting images with selectors:', selectors);
      
      const images = [];
      const seenUrls = new Set();

      for (const selector of selectors) {
        try {
          const elements = document.querySelectorAll(selector);
          this.log(`Found ${elements.length} images with selector: ${selector}`);
          
          elements.forEach((img, index) => {
            let imageUrl = img.src || 
                          img.getAttribute('data-src') || 
                          img.getAttribute('data-lazy-src') ||
                          img.getAttribute('data-original') ||
                          img.getAttribute('srcset')?.split(' ')[0];
            
            if (imageUrl && !seenUrls.has(imageUrl)) {
              // Convert relative URLs to absolute
              imageUrl = this.normalizeUrl(imageUrl);
              
              seenUrls.add(imageUrl);
              images.push({
                imageUrl: imageUrl,
                isPrimary: images.length === 0, // First image is primary
                sortOrder: images.length
              });
              
              this.log(`Added image: ${imageUrl}`);
            }
          });
        } catch (error) {
          console.warn(`Failed to extract images from ${selector}:`, error);
        }
      }

      this.log(`Total images extracted: ${images.length}`);
      return images;
    },

    // Normalize URL to absolute URL
    normalizeUrl(url) {
      if (!url) return '';
      
      try {
        // Already absolute URL
        if (url.startsWith('http://') || url.startsWith('https://')) {
          return url;
        }
        
        // Protocol-relative URL
        if (url.startsWith('//')) {
          return window.location.protocol + url;
        }
        
        // Root-relative URL
        if (url.startsWith('/')) {
          return window.location.origin + url;
        }
        
        // Relative URL
        return new URL(url, window.location.href).href;
      } catch (error) {
        console.warn('Failed to normalize URL:', url, error);
        return url;
      }
    },

    // Get current page URL
    getCurrentUrl() {
      return window.location.href;
    },

    // Detect marketplace from URL with better detection
    detectMarketplace() {
      const url = window.location.hostname.toLowerCase();
      
      if (url.includes('etsy.com')) return 'etsy';
      if (url.includes('ebay.com')) return 'ebay';  
      if (url.includes('amazon.com')) return 'amazon';
      if (url.includes('shopify.com')) return 'shopify';
      if (url.includes('woocommerce.com')) return 'woocommerce';
      
      return 'unknown';
    },

    // Extract price information with better parsing
    extractPrice(selectors) {
      this.log('Extracting price with selectors:', selectors);
      
      for (const selector of selectors) {
        try {
          const element = document.querySelector(selector);
          if (element) {
            const priceText = this.cleanText(element.textContent);
            this.log(`Price text found: "${priceText}"`);
            
            // More robust price matching
            const priceMatch = priceText.match(/[\$€£¥]?[\d,]+\.?\d*/);
            
            if (priceMatch) {
              const amount = priceMatch[0].replace(/[^\d.]/g, '');
              const currency = this.extractCurrency(priceText);
              
              const priceData = {
                raw: priceText,
                amount: amount,
                currency: currency
              };
              
              this.log('Extracted price:', priceData);
              return priceData;
            }
          }
        } catch (error) {
          console.warn(`Failed to extract price from ${selector}:`, error);
        }
      }
      
      this.log('No price found');
      return null;
    },

    // Extract currency from price text
    extractCurrency(priceText) {
      if (priceText.includes('$')) return 'USD';
      if (priceText.includes('€')) return 'EUR';
      if (priceText.includes('£')) return 'GBP';
      if (priceText.includes('¥')) return 'JPY';
      if (priceText.includes('₫')) return 'VND';
      
      return 'USD'; // Default
    },

    // Extract rating information
    extractRating(selectors) {
      this.log('Extracting rating with selectors:', selectors);
      
      for (const selector of selectors) {
        try {
          const element = document.querySelector(selector);
          if (element) {
            const ratingText = this.cleanText(element.textContent);
            this.log(`Rating text found: "${ratingText}"`);
            
            const ratingMatch = ratingText.match(/(\d+\.?\d*)/);
            
            if (ratingMatch) {
              const rating = ratingMatch[1];
              this.log(`Extracted rating: ${rating}`);
              return rating;
            }
          }
        } catch (error) {
          console.warn(`Failed to extract rating from ${selector}:`, error);
        }
      }
      
      this.log('No rating found');
      return null;
    },

    // Send extracted data to background script
    async sendToBackground(productData) {
      this.log('Sending data to background:', productData);
      
      try {
        if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
          const response = await chrome.runtime.sendMessage({
            type: 'EXTRACT_PRODUCT',
            data: productData
          });
          
          this.log('Background response:', response);
          return response;
        } else {
          throw new Error('Chrome extension API not available');
        }
      } catch (error) {
        console.error('Failed to send data to background:', error);
        throw error;
      }
    },

    // Show notification to user
    showNotification(message, type = 'info') {
      this.log(`Showing notification: ${message} (${type})`);
      
      // Remove existing notifications
      const existing = document.querySelectorAll('.common-extractor-notification');
      existing.forEach(el => el.remove());
      
      // Create notification
      const notification = document.createElement('div');
      notification.className = 'common-extractor-notification';
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        z-index: 999999;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        max-width: 350px;
        word-wrap: break-word;
        animation: slideIn 0.3s ease-out;
      `;
      
      // Add animation keyframes
      if (!document.querySelector('#common-extractor-styles')) {
        const style = document.createElement('style');
        style.id = 'common-extractor-styles';
        style.textContent = `
          @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
          }
        `;
        document.head.appendChild(style);
      }
      
      notification.textContent = message;
      document.body.appendChild(notification);
      
      // Auto remove after 4 seconds
      setTimeout(() => {
        if (notification.parentNode) {
          notification.style.animation = 'slideIn 0.3s ease-out reverse';
          setTimeout(() => {
            if (notification.parentNode) {
              notification.parentNode.removeChild(notification);
            }
          }, 300);
        }
      }, 4000);
    },

    // Validate extracted product data
    validateProductData(productData) {
      const errors = [];
      
      if (!productData.title || productData.title.length < 3) {
        errors.push('Product title is required and must be at least 3 characters');
      }
      
      if (!productData.productUrl) {
        errors.push('Product URL is required');
      }
      
      if (!productData.marketplace) {
        errors.push('Marketplace is required');
      }
      
      if (!productData.images || productData.images.length === 0) {
        errors.push('At least one product image is required');
      }
      
      const validation = {
        isValid: errors.length === 0,
        errors: errors
      };
      
      this.log('Validation result:', validation);
      return validation;
    },

    // Initialize - can be called to ensure everything is ready
    initialize() {
      this.log('CommonExtractor initialized successfully');
      this.log('Available methods:', Object.getOwnPropertyNames(this).filter(name => typeof this[name] === 'function'));
      return true;
    }
  };

  // Make it globally available
  window.CommonExtractor = CommonExtractor;
  
  // Initialize immediately
  CommonExtractor.initialize();
  
  console.log('CommonExtractor loaded and ready!');
})();